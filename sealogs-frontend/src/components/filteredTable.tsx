// filteredTable.tsx
'use client'
import dynamic from 'next/dynamic'
import {
    Table,
    TableBody,
    TableHeader,
    TableFooter,
    TableRow,
    TableHead,
    TableCell,
} from '@/components/ui/table'
import { DataTableToolbar } from './data-table-toolbar'
import { DataTablePagination } from './data-table-pagination'
import * as React from 'react'
import {
    ColumnDef,
    ColumnFiltersState,
    SortingState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from '@tanstack/react-table'
import { Card } from './ui/card'

// Extended ColumnDef type with cellAlignment property
export type ExtendedColumnDef<TData, TValue = unknown> = ColumnDef<
    TData,
    TValue
> & {
    /** Controls the text alignment for this column's header and cells. Defaults to 'center'. Note: 'title' column is always left-aligned. */
    cellAlignment?: 'left' | 'center' | 'right'
}

/**
 * Helper function to create columns with proper typing inference
 * Eliminates the need to explicitly type column arrays
 */
export function createColumns<TData = any>(
    columns: ExtendedColumnDef<TData, any>[],
): ExtendedColumnDef<TData, any>[] {
    return columns
}

interface DataTableProps<TData, TValue> {
    columns: ExtendedColumnDef<TData, TValue>[]
    data: TData[]
    showToolbar?: boolean
    className?: string
    pageSize?: number
    pageSizeOptions?: number[]
    showPageSizeSelector?: boolean
    onChange?: any
}

// Helper function to get alignment classes based on cellAlignment prop
const getAlignmentClasses = (alignment: 'left' | 'center' | 'right') => {
    switch (alignment) {
        case 'left':
            return 'items-left justify-items-start text-left'
        case 'right':
            return 'items-right justify-items-end text-right'
        case 'center':
        default:
            return 'items-center justify-items-center text-center'
    }
}

export function DataTable<TData, TValue>({
    columns,
    data,
    showToolbar = true,
    className,
    pageSize = 10,
    pageSizeOptions = [10, 20, 30, 40, 50],
    showPageSizeSelector = true,
    onChange,
}: DataTableProps<TData, TValue>) {
    const [sorting, setSorting] = React.useState<SortingState>([])
    const [columnFilters, setColumnFilters] =
        React.useState<ColumnFiltersState>([])
    const [pagination, setPagination] = React.useState({
        pageIndex: 0,
        pageSize: pageSize,
    })

    // Update pagination when pageSize prop changes
    React.useEffect(() => {
        setPagination((prev) => ({
            ...prev,
            pageSize: pageSize,
        }))
    }, [pageSize])

    const table = useReactTable({
        data,
        columns,
        onSortingChange: setSorting,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        onColumnFiltersChange: setColumnFilters,
        getFilteredRowModel: getFilteredRowModel(),
        onPaginationChange: setPagination,
        state: {
            sorting,
            columnFilters,
            pagination,
        },
    })

    return (
        <div className="space-y-4">
            {showToolbar && (
                <Card className="p-2 md:p-auto">
                    <DataTableToolbar table={table} onChange={onChange} />
                </Card>
            )}

            <Table
                className={
                    className ||
                    'p-0 phablet:p-8 lg:p-6 xl:p-8 shadow-none border-0 phablet:border border-border bg-background rounded-lg'
                }>
                {table
                    .getHeaderGroups()
                    .some((headerGroup) =>
                        headerGroup.headers.some(
                            (header) =>
                                header.column.columnDef.header &&
                                header.column.columnDef.header !== '',
                        ),
                    ) && (
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => {
                                    const columnDef = header.column
                                        .columnDef as ExtendedColumnDef<
                                        TData,
                                        TValue
                                    >
                                    const alignment =
                                        header.column.id === 'title'
                                            ? 'left'
                                            : columnDef.cellAlignment ||
                                              'center'

                                    return (
                                        <TableHead
                                            key={header.id}
                                            className={
                                                header.column.id === 'title'
                                                    ? 'items-left justify-items-start text-left'
                                                    : getAlignmentClasses(
                                                          alignment,
                                                      )
                                            }>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                      header.column.columnDef
                                                          .header,
                                                      header.getContext(),
                                                  )}
                                        </TableHead>
                                    )
                                })}
                            </TableRow>
                        ))}
                    </TableHeader>
                )}
                <TableBody>
                    {table.getRowModel().rows.length ? (
                        table.getRowModel().rows.map((row) => (
                            <TableRow
                                key={String(row.id)}
                                data-state={
                                    row.getIsSelected() ? 'selected' : undefined
                                }>
                                {row.getVisibleCells().map((cell) => {
                                    const columnDef = cell.column
                                        .columnDef as ExtendedColumnDef<
                                        TData,
                                        TValue
                                    >
                                    const alignment =
                                        cell.column.id === 'title'
                                            ? 'left'
                                            : columnDef.cellAlignment ||
                                              'center'

                                    return (
                                        <TableCell
                                            key={cell.id}
                                            className={
                                                cell.column.id === 'title'
                                                    ? `${columns.length > 2 ? 'w-auto' : 'w-full'} items-left justify-items-start text-left`
                                                    : getAlignmentClasses(
                                                          alignment,
                                                      )
                                            }>
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext(),
                                            )}
                                        </TableCell>
                                    )
                                })}
                            </TableRow>
                        ))
                    ) : (
                        <TableRow>
                            <TableCell
                                colSpan={columns.length}
                                className="h-24 text-center">
                                No results.
                            </TableCell>
                        </TableRow>
                    )}
                </TableBody>
            </Table>

            {(table.getCanPreviousPage() || table.getCanNextPage()) && (
                <div className="flex items-center justify-end space-x-2 py-4">
                    <DataTablePagination
                        table={table}
                        pageSizeOptions={pageSizeOptions}
                        showPageSizeSelector={showPageSizeSelector}
                    />
                </div>
            )}
        </div>
    )
}
