'use client'

import * as React from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { DayPicker } from 'react-day-picker'
import { format } from 'date-fns'

import { Button, buttonVariants } from '@/components/ui/button'
import { cn } from '@/app/lib/utils'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from './select'
import { ScrollArea } from './scroll-area'

export type CalendarProps = React.ComponentProps<typeof DayPicker> & {
    /** Show or hide the year-picker UI */
    showYearPicker?: boolean
    /** If false, year lists stop at the current year (no future years) */
    allowFutureYears?: boolean
}

interface YearSelectionProps {
    calendarView: Date
    onYearChange: (nextMonth: Date) => void
    allowFutureYears?: boolean
}

const YearSelection = React.memo(function YearSelection({
    calendarView,
    onYearChange,
    allowFutureYears = true,
}: YearSelectionProps) {
    const listRef = React.useRef<HTMLDivElement>(null)
    const prevYearRef = React.useRef(calendarView.getFullYear())

    const years = React.useMemo(() => {
        const current = new Date().getFullYear()
        const start = current - 100
        const end = allowFutureYears ? current + 100 : current
        return Array.from({ length: end - start + 1 }, (_, i) => start + i)
    }, [allowFutureYears])

    const realCurrentYear = new Date().getFullYear()

    React.useEffect(() => {
        const el = listRef.current?.querySelector<HTMLElement>(
            `[data-year="${calendarView.getFullYear()}"]`,
        )
        if (el) el.scrollIntoView({ block: 'center', behavior: 'auto' })
    }, [])

    React.useEffect(() => {
        const newYear = calendarView.getFullYear()
        if (prevYearRef.current !== newYear) {
            const el = listRef.current?.querySelector<HTMLElement>(
                `[data-year="${newYear}"]`,
            )
            if (el) el.scrollIntoView({ block: 'center', behavior: 'smooth' })
            prevYearRef.current = newYear
        }
    }, [calendarView])

    const handleYearClick = (year: number) => {
        const next = new Date(calendarView)
        next.setFullYear(year)
        onYearChange(next)
    }

    return (
        <div className="flex h-full flex-col justify-start">
            <div className="px-3 py-2 text-sm font-medium">Select Year</div>
            <ScrollArea
                ref={listRef}
                className="flex-1 scroll-smooth"
                style={{ scrollPaddingTop: 40, scrollPaddingBottom: 40 }}>
                <div className="h-[40px]" aria-hidden="true" />
                <div className="flex flex-col">
                    {years.map((y) => (
                        <Button
                            key={y}
                            data-year={y}
                            onClick={() => handleYearClick(y)}
                            variant={
                                y === calendarView.getFullYear()
                                    ? 'primary'
                                    : y === realCurrentYear
                                      ? 'secondary'
                                      : 'ghost'
                            }
                            className={cn(
                                'text-left px-2 h-[40px] my-1 w-full',
                            )}>
                            {y}
                        </Button>
                    ))}
                </div>
                <div className="h-[40px]" aria-hidden="true" />
            </ScrollArea>
        </div>
    )
})

export function Calendar({
    className,
    classNames,
    showOutsideDays = true,
    month: controlledMonth,
    onMonthChange,
    showYearPicker = true,
    allowFutureYears = true,
    ...props
}: CalendarProps) {
    const [calendarView, setCalendarView] = React.useState<Date>(
        () => controlledMonth || new Date(),
    )

    React.useEffect(() => {
        if (controlledMonth) {
            setCalendarView(controlledMonth)
        }
    }, [controlledMonth])

    const years = React.useMemo(() => {
        const current = new Date().getFullYear()
        const start = current - 100
        const end = allowFutureYears ? current + 100 : current
        return Array.from({ length: end - start + 1 }, (_, i) => start + i)
    }, [allowFutureYears])

    const handleMonthChange = (newMonth: Date) => {
        setCalendarView(newMonth)
        onMonthChange?.(newMonth)
    }

    const handleYearChangeSelect = (year: number) => {
        const next = new Date(calendarView)
        next.setFullYear(year)
        setCalendarView(next)
        onMonthChange?.(next)
    }

    return (
        <>
            {showYearPicker && (
                <div className="block small:hidden border-b border-border">
                    <div className="flex items-center justify-between p-3">
                        <div className="text-sm font-medium">
                            {format(calendarView, 'MMMM yyyy')}
                        </div>
                        <Select
                            value={calendarView.getFullYear().toString()}
                            onValueChange={(val) =>
                                handleYearChangeSelect(Number(val))
                            }>
                            <SelectTrigger className="h-9 w-[100px] rounded-md border border-input bg-background px-3 text-sm shadow-sm">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                {years.map((year) => (
                                    <SelectItem
                                        key={year}
                                        value={year.toString()}>
                                        {year}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                </div>
            )}

            <div className={cn('flex flex-col small:flex-row p-3', className)}>
                <DayPicker
                    showOutsideDays={showOutsideDays}
                    month={calendarView}
                    onMonthChange={handleMonthChange}
                    className={cn('flex-1', className)}
                    classNames={{
                        months: 'flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0',
                        month: 'space-y-6',
                        caption:
                            'flex justify-center pt-1 relative items-center',
                        caption_label: 'font-medium',
                        nav: 'space-x-1 flex items-center',
                        nav_button: cn(
                            buttonVariants({ variant: 'outline' }),
                            'h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100',
                        ),
                        nav_button_previous: 'absolute left-1',
                        nav_button_next: 'absolute right-1',
                        table: 'w-full border-collapse space-y-1',
                        head_row: 'flex w-full',
                        head_cell:
                            'relative flex-1 text-center text-muted-foreground rounded-md',
                        row: 'flex w-full mt-2',
                        cell: cn(
                            'relative p-2 text-center focus-within:z-20 [&:has([aria-selected])]:bg-accent',
                            props.mode === 'range'
                                ? '[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md'
                                : '[&:has([aria-selected])]:rounded-md',
                        ),
                        day: cn(
                            buttonVariants({ variant: 'ghost' }),
                            'size-8 p-0 aria-selected:opacity-100',
                        ),
                        day_range_start: 'day-range-start',
                        day_range_end: 'day-range-end',
                        day_selected:
                            'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',
                        day_today: 'bg-outer-space-50 text-primary',
                        day_outside:
                            'day-outside text-muted-foreground aria-selected:bg-accent/50',
                        day_disabled: 'text-muted-foreground opacity-50',
                        day_range_middle:
                            'aria-selected:bg-accent aria-selected:text-primary',
                        day_hidden: 'invisible',
                        ...classNames,
                    }}
                    components={{
                        IconLeft: ({ className, ...p }) => (
                            <ChevronLeft
                                className={cn('h-4 w-4', className)}
                                {...p}
                            />
                        ),
                        IconRight: ({ className, ...p }) => (
                            <ChevronRight
                                className={cn('h-4 w-4', className)}
                                {...p}
                            />
                        ),
                    }}
                    {...props}
                />

                {showYearPicker && (
                    <div
                        className={cn(
                            'hidden border-l border-border small:block pl-2 ml-2',
                            props.mode === 'range'
                                ? 'max-h-[580px]'
                                : 'max-h-[350px]',
                        )}>
                        <YearSelection
                            calendarView={calendarView}
                            onYearChange={handleMonthChange}
                            allowFutureYears={allowFutureYears}
                        />
                    </div>
                )}
            </div>
        </>
    )
}

Calendar.displayName = 'Calendar'
