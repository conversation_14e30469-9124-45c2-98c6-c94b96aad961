'use client'

import * as React from 'react'
import * as SheetPrimitive from '@radix-ui/react-dialog'
import { cva, type VariantProps } from 'class-variance-authority'
import { ChevronUp, ChevronDown, ChevronLeft, ChevronRight } from 'lucide-react'

import { cn } from '@/app/lib/utils'
import { H3 } from './typography'
import { ScrollArea } from './scroll-area'

// Create a context to share the side prop
type SheetSideContextType = 'top' | 'bottom' | 'left' | 'right'
const SheetSideContext = React.createContext<SheetSideContextType>('right')

const Sheet = SheetPrimitive.Root

const SheetTrigger = SheetPrimitive.Trigger

const SheetClose = SheetPrimitive.Close

const SheetPortal = SheetPrimitive.Portal

const SheetOverlay = React.forwardRef<
    React.ElementRef<typeof SheetPrimitive.Overlay>,
    React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>
>(({ className, ...props }, ref) => (
    <SheetPrimitive.Overlay
        className={cn(
            'fixed inset-0 z-50 bg-muted/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
            className,
        )}
        {...props}
        ref={ref}
    />
))
SheetOverlay.displayName = SheetPrimitive.Overlay.displayName

const sheetVariants = cva(
    'fixed z-50 gap-4 bg-background flex flex-col shadow-lg border border-border transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out',
    {
        variants: {
            side: {
                top: 'inset-x-5 top-0 border-b h-[60svh] w-full data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top',
                bottom: 'inset-x-5 bottom-0 border-t h-[60svh] w-full data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom',
                left: 'inset-y-[31px] border-l-0 rounded-r-[6px] left-0 w-3/4 data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-fit sm:min-w-sm',
                right: 'inset-y-[31px] border-r-0 rounded-l-[6px] right-0 w-11/12 md:w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-fit sm:min-w-sm',
            },
        },
        defaultVariants: {
            side: 'right',
        },
    },
)

interface SheetContentProps
    extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,
        VariantProps<typeof sheetVariants> {}

const SheetContent = React.forwardRef<
    React.ElementRef<typeof SheetPrimitive.Content>,
    SheetContentProps
>(({ side = 'right', className, children, ...props }, ref) => {
    // Define position and rotation classes based on side
    const closeButtonPositionClasses = {
        top: 'absolute left-1/2 -translate-x-1/2 -top-[21px] size-[42px]',
        bottom: 'absolute left-1 -top-[21px] size-[42px]',
        left: 'absolute -right-[21px] top-[34px] size-[42px]',
        right: 'absolute -left-[21px] top-[34px] size-[42px]',
    }

    // Define the appropriate icon for each side
    const CloseIcon = React.useMemo(() => {
        switch (side) {
            case 'top':
                return ChevronUp
            case 'bottom':
                return ChevronDown
            case 'left':
                return ChevronLeft
            case 'right':
                return ChevronRight
            default:
                return ChevronRight
        }
    }, [side])

    return (
        <SheetPortal>
            <SheetOverlay />
            <SheetSideContext.Provider value={side as SheetSideContextType}>
                <SheetPrimitive.Content
                    ref={ref}
                    className={cn(sheetVariants({ side }), className)}
                    {...props}>
                    <SheetPrimitive.Close
                        className={cn(
                            closeButtonPositionClasses[
                                side as keyof typeof closeButtonPositionClasses
                            ],
                            'rounded-full flex items-center justify-center text-cool-grey-500 bg-background border border-border transition-opacity focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary',
                        )}>
                        <CloseIcon className="size-6" />
                        <span className="sr-only">Close</span>
                    </SheetPrimitive.Close>
                    {children}
                </SheetPrimitive.Content>
            </SheetSideContext.Provider>
        </SheetPortal>
    )
})
SheetContent.displayName = SheetPrimitive.Content.displayName

interface SheetHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
    side?: 'top' | 'bottom' | 'left' | 'right'
}

const SheetHeader = ({
    className,
    side: propSide,
    ...props
}: SheetHeaderProps) => {
    // Use the side from context if not provided as a prop
    const contextSide = React.useContext(SheetSideContext)
    const side = propSide || contextSide

    // Define rounded corner classes based on side
    const roundedClasses = {
        top: 'rounded-b-[6px]',
        bottom: 'rounded-t-[6px]',
        left: 'rounded-se-[6px]',
        right: 'rounded-ss-[6px] items-center',
    }

    return (
        <div
            className={cn(
                'flex px-7 flex-col h-[82px] pt-[26px] pb-[25px] justify-center text-background bg-card-foreground space-y-2',
                roundedClasses[side as keyof typeof roundedClasses],
                className,
            )}
            {...props}
        />
    )
}
SheetHeader.displayName = 'SheetHeader'

const SheetFooter = ({
    className,
    ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
    <div
        className={cn(
            'flex flex-col-reverse p-5 border-t border-border sm:flex-row sm:justify-end sm:space-x-2',
            className,
        )}
        {...props}
    />
)
SheetFooter.displayName = 'SheetFooter'

const SheetBody = ({
    className,
    ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
    <ScrollArea className="flex-1 h-full">
        <div
            className={cn(
                'flex flex-col px-7 py-6 space-y-4 leading-7',
                className,
            )}
            {...props}
        />
    </ScrollArea>
)
SheetBody.displayName = 'SheetBody'

const SheetTitle = React.forwardRef<
    React.ElementRef<typeof SheetPrimitive.Title>,
    React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>
>(({ className, ...props }, ref) => (
    <SheetPrimitive.Title
        ref={ref}
        className={cn('text-foreground', className)}
        {...props}>
        <H3 className="text-background">{props.children}</H3>
    </SheetPrimitive.Title>
))
SheetTitle.displayName = SheetPrimitive.Title.displayName

const SheetDescription = React.forwardRef<
    React.ElementRef<typeof SheetPrimitive.Description>,
    React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>
>(({ className, ...props }, ref) => (
    <SheetPrimitive.Description
        ref={ref}
        className={cn(' text-muted-foreground', className)}
        {...props}
    />
))
SheetDescription.displayName = SheetPrimitive.Description.displayName

export {
    Sheet,
    SheetPortal,
    SheetOverlay,
    SheetTrigger,
    SheetClose,
    SheetContent,
    SheetHeader,
    SheetBody,
    SheetFooter,
    SheetTitle,
    SheetDescription,
}
