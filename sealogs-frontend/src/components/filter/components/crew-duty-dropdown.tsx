'use client'

import { CREW_DUTY } from '@/app/lib/graphQL/query'
import { useLazyQuery } from '@apollo/client'
import { useEffect, useState } from 'react'
import Select from 'react-select'
import { CrewDuty } from '../../../../types/crew-duty'
import CrewDutyModel from '@/app/offline/models/crewDuty'
import {
    Dialog,
    DialogTrigger,
    Modal,
    ModalOverlay,
} from 'react-aria-components'
import CrewDutyForm from '../../../app/ui/crew-duty/form'

import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { useOnline } from '@reactuses/core'
import { Combobox } from '@/components/ui/comboBox'
interface CrewDutyDropdownProps {
    crewDutyID?: number
    onChange: (value: any) => void
    // duties: any
    isClearable?: boolean
    controlClasses?: 'default' | 'filter'
    filterCustomClassName?: string
    offline?: boolean
    placeholder?: 'Duty'
    label?: string
    multi?: boolean
    hideCreateOption?: boolean
}
const CrewDutyDropdown = ({
    crewDutyID = 0,
    onChange,
    label,
    // duties,
    isClearable = false,
    controlClasses = 'default',
    filterCustomClassName,
    offline = false,
    placeholder = 'Duty',
    multi = true,
    hideCreateOption = false,
}: CrewDutyDropdownProps) => {
    const dutyModel = new CrewDutyModel()
    const online = false // To be replaced with useOnline()
    const [isLoading, setIsLoading] = useState(true)
    const [duties, setDuties] = useState([] as any)
    const [duty, setDuty] = useState([] as any)
    const [openDialog, setOpenDialog] = useState(false)
    const [
        queryDuties,
        {
            loading: queryDutiesLoading,
            error: queryDutiesError,
            data: queryDutiesData,
        },
    ] = useLazyQuery(CREW_DUTY, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readCrewDuties.nodes
            if (data) {
                const activeDuties = data.filter((duty: any) => !duty.archived)
                let finalDuties = activeDuties

                if (!hideCreateOption) {
                    const dutyObject: CrewDuty = {
                        id: 0,
                        title: '-- Create New Duty --',
                        archived: false,
                        abbreviation: 'NEW',
                    }
                    finalDuties = [dutyObject, ...activeDuties]
                }

                setDuties(finalDuties)
                if (crewDutyID > 0) {
                    const selectedDuty = activeDuties.find(
                        (d: any) => d.id === crewDutyID,
                    )
                    setDuty({
                        label: selectedDuty.title,
                        value: selectedDuty.id,
                    })
                }
            }
        },
        onError: (error: any) => {
            console.error('queryDutiesEntry error', error)
        },
    })
    const loadCrewDuties = async () => {
        if (offline) {
            const data = await dutyModel.getAll()
            if (data) {
                const activeDuties = data.filter((duty: any) => !duty.archived)
                let finalDuties = activeDuties

                if (!hideCreateOption) {
                    const dutyObject: CrewDuty = {
                        id: 0,
                        title: '-- Create New Duty --',
                        archived: false,
                        abbreviation: 'NEW',
                    }
                    finalDuties = [dutyObject, ...activeDuties]
                }

                setDuties(finalDuties)
                if (crewDutyID > 0) {
                    const selectedDuty = activeDuties.find(
                        (d: any) => d.id === crewDutyID,
                    )
                    setDuty({
                        label: selectedDuty.title,
                        value: selectedDuty.id,
                    })
                }
            }
        } else {
            await queryDuties()
        }
    }
    const handleOnChange = (selectedOptions: any) => {
        if (multi) {
            setDuty(selectedOptions) // Store an array of selected values
            onChange(selectedOptions) // Pass the selected array
        } else {
            setDuty(selectedOptions)
            onChange(selectedOptions)
        }
    }
    const handleCreateCrewDuty = (crewDuty: any) => {
        const newDuty = { label: crewDuty.title, value: crewDuty.id }
        setDuty(newDuty)
        onChange(newDuty)
        loadCrewDuties()
        setOpenDialog(false)
    }
    useEffect(() => {
        if (isLoading) {
            loadCrewDuties()
            setIsLoading(false)
        }
    }, [isLoading])

    const [permissions, setPermissions] = useState<any>(false)
    const [edit_logBookEntry, setEdit_logBookEntry] = useState<any>(false)

    const init_permissions = () => {
        if (permissions) {
            if (
                hasPermission(
                    process.env.EDIT_LOGBOOKENTRY || 'EDIT_LOGBOOKENTRY',
                    permissions,
                )
            ) {
                setEdit_logBookEntry(true)
            } else {
                setEdit_logBookEntry(false)
            }
        }
    }

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
    }, [])

    useEffect(() => {
        init_permissions()
    }, [permissions])

    useEffect(() => {
        if (crewDutyID > 0 && duties.length > 0) {
            const selectedDuty = duties.find((d: any) => d.id === crewDutyID)
            if (selectedDuty) {
                setDuty({ label: selectedDuty.title, value: selectedDuty.id })
            }
        } else if (crewDutyID === 0) {
            setDuty(null)
        }
    }, [crewDutyID, duties])

    return (
        <>
            <Combobox
                options={duties.map((d: any) => ({
                    label: `${d.title}`,
                    value: d.id,
                }))}
                label={label}
                multi={multi}
                value={duty}
                onChange={handleOnChange}
                placeholder={placeholder}
            />
        </>
    )
}

export default CrewDutyDropdown
