'use client'

import { PlusCircle, MinusCircle } from 'lucide-react'
import { useState } from 'react'
import DatePicker from '@/components/DateRange'
import {
    Toolt<PERSON>,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from '@/components/ui/tooltip'
import { parseDateWithoutTimezone } from '@/app/helpers/dateHelper'

export default function LogDate({
    log_params,
    setStartDate,
    setEndDate,
    edit_logBookEntry = false,
}: {
    log_params: any
    setStartDate: any
    setEndDate: any
    edit_logBookEntry?: boolean
}) {
    console.log('25 log_params', log_params)
    const [startValue, setStartValue] = useState(
        parseDateWithoutTimezone(log_params.startDate || new Date()),
    )
    const [endValue, setEndValue] = useState(
        parseDateWithoutTimezone(
            log_params.endDate || log_params.startDate || new Date(),
        ),
    )
    const [showButton, setShowButton] = useState(true)

    const handleStartChange = (date: Date) => {
        setStartValue(date)
        setStartDate(date)
    }

    const handleEndChange = (date: Date) => {
        setEndValue(date)
        setEndDate(date)
    }

    const handleShowEndDate = () => {
        setShowButton(false)
    }

    const handleHideEndDate = () => {
        setShowButton(true)
    }

    return (
        <div
            className={`w-full flex flex-wrap items-end gap-x-5 gap-y-8 flex-grow transition-all duration-200 ease-in-out ${edit_logBookEntry ? '' : 'opacity-80 pointer-events-none'}`}>
            <DatePicker
                onChange={handleStartChange}
                mode="single"
                type="date"
                label={log_params.startLabel}
                value={startValue}
                placeholder={new Date().toLocaleDateString()}
                disabled={!edit_logBookEntry}
            />

            {!showButton && (
                <DatePicker
                    onChange={handleEndChange}
                    mode="single"
                    type="date"
                    label={log_params.endLabel}
                    value={endValue}
                    placeholder={new Date().toLocaleDateString()}
                    disabled={!edit_logBookEntry}
                />
            )}
            <div className="w-fit flex items-center h-11">
                {!log_params.showOvernightCheckbox && showButton && (
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger
                                className="p-0 text-cool-grey-200"
                                onClick={handleShowEndDate}>
                                <PlusCircle
                                    size={28}
                                    className="stroke-[2px]"
                                />
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Add end date</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                )}

                {!showButton && (
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger
                                onClick={handleHideEndDate}
                                className="p-0 text-cool-grey-200">
                                <MinusCircle
                                    size={28}
                                    className="stroke-[2px]"
                                />
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Remove end date</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                )}
            </div>
        </div>
    )
}
