'use client'

import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import { Heading } from 'react-aria-components'
import { TimePicker } from '@/components/ui/time-picker'
import DatePicker from '@/components/DateRange'
import { Button } from '@/components/ui/button'
import {
    CreateEventType_PersonRescue,
    UpdateEventType_PersonRescue,
    CreateCGEventMission,
    UpdateCGEventMission,
    CreateTripEvent,
    UpdateTripEvent,
    CreateMissionTimeline,
    UpdateMissionTimeline,
} from '@/app/lib/graphQL/mutation'
import { GetTripEvent } from '@/app/lib/graphQL/query'
import { Combobox } from '@/components/ui/comboBox'
import { getSeaLogsMembersList } from '@/app/lib/actions'
import Editor from '../../editor'
import { useLazyQuery, useMutation } from '@apollo/client'
import { Check, Plus, Pencil } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useSearchParams } from 'next/navigation'

import { formatDateTime } from '@/app/helpers/dateHelper'
import SeaLogsMemberModel from '@/app/offline/models/seaLogsMember'
import TripEventModel from '@/app/offline/models/tripEvent'
import EventType_PersonRescueModel from '@/app/offline/models/eventType_PersonRescue'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import CGEventMissionModel from '@/app/offline/models/cgEventMission'
import MissionTimelineModel from '@/app/offline/models/missionTimeline'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

import { AlertDialogNew, Textarea } from '@/components/ui'

export default function PersonRescue({
    geoLocations,
    currentTrip = false,
    updateTripReport,
    selectedEvent = false,
    tripReport,
    closeModal,
    locked,
    offline = false,
}: {
    geoLocations: any
    currentTrip: any
    updateTripReport: any
    selectedEvent: any
    tripReport: any
    closeModal: any
    locked: any
    offline?: boolean
}) {
    const { toast } = useToast()
    const searchParams = useSearchParams()
    const vesselID = searchParams.get('vesselID') ?? 0
    const [locations, setLocations] = useState<any>(false)
    const [time, setTime] = useState<any>(dayjs().format('HH:mm'))
    const [openCommentsDialog, setOpenCommentsDialog] = useState(false)
    const [commentTime, setCommentTime] = useState<any>()
    const [members, setMembers] = useState<any>(false)
    const [content, setContent] = useState<any>()
    const [rescueData, setRescueData] = useState<any>(false)
    const [missionData, setMissionData] = useState<any>(false)
    const [commentData, setCommentData] = useState<any>(false)
    const [timeline, setTimeline] = useState<any>(false)
    const [currentEvent, setCurrentEvent] = useState<any>(selectedEvent)
    const seaLogsMemberModel = new SeaLogsMemberModel()
    const tripEventModel = new TripEventModel()
    const personRescueModel = new EventType_PersonRescueModel()
    const cgEventMissionModel = new CGEventMissionModel()
    const missionTimelineModel = new MissionTimelineModel()

    useEffect(() => {
        setRescueData(false)
        if (selectedEvent) {
            setCurrentEvent(selectedEvent)
            getCurrentEvent(selectedEvent?.id)
        }
    }, [selectedEvent])

    useEffect(() => {
        setRescueData(false)
        if (currentEvent) {
            getCurrentEvent(currentEvent?.id)
        }
    }, [currentEvent])

    const getCurrentEvent = async (id: any) => {
        if (offline) {
            const event = await tripEventModel.getById(id)
            if (event) {
                setRescueData({
                    personName: event.eventType_PersonRescue?.personName,
                    gender: event.eventType_PersonRescue?.gender,
                    age: event.eventType_PersonRescue?.age,
                    personDescription:
                        event.eventType_PersonRescue?.personDescription,
                    cgMembershipNumber:
                        event.eventType_PersonRescue?.cgMembershipNumber,
                    personOtherDetails:
                        event.eventType_PersonRescue?.personOtherDetails,
                    cgMembershipType: 'cgnz',
                    missionID: event.eventType_PersonRescue?.missionID,
                    operationDescription:
                        event.eventType_PersonRescue?.operationDescription,
                    operationType: event.eventType_PersonRescue?.operationType
                        ? operationType.filter((operation: any) =>
                              event.eventType_PersonRescue?.operationType
                                  .split(',')
                                  .includes(operation.value),
                          )
                        : [],
                })
                setTime(event.eventType_PersonRescue?.mission?.completedAt)
                setMissionData({
                    missionType:
                        event.eventType_PersonRescue?.mission?.missionType?.replaceAll(
                            '_',
                            ' ',
                        ),
                    description:
                        event.eventType_PersonRescue?.mission?.description,
                    operationOutcome:
                        event.eventType_PersonRescue?.mission?.operationOutcome?.replaceAll(
                            '_',
                            ' ',
                        ),
                    currentLocationID:
                        event.eventType_PersonRescue?.mission?.currentLocation
                            ?.id,
                    operationDescription:
                        event.eventType_PersonRescue?.mission
                            ?.operationDescription,
                })
                setTimeline(
                    event.eventType_PersonRescue?.mission?.missionTimeline
                        ?.nodes,
                )
            }
        } else {
            getTripEvent({
                variables: {
                    id: id,
                },
            })
        }
    }

    const [getTripEvent] = useLazyQuery(GetTripEvent, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const event = response.readOneTripEvent
            if (event) {
                setRescueData({
                    personName: event.eventType_PersonRescue?.personName,
                    gender: event.eventType_PersonRescue?.gender,
                    age: event.eventType_PersonRescue?.age,
                    personDescription:
                        event.eventType_PersonRescue?.personDescription,
                    cgMembershipNumber:
                        event.eventType_PersonRescue?.cgMembershipNumber,
                    personOtherDetails:
                        event.eventType_PersonRescue?.personOtherDetails,
                    cgMembershipType: 'cgnz',
                    missionID: event.eventType_PersonRescue?.missionID,
                    operationDescription:
                        event.eventType_PersonRescue?.operationDescription,
                    operationType: event.eventType_PersonRescue?.operationType
                        ? operationType.filter((operation: any) =>
                              event.eventType_PersonRescue?.operationType
                                  .split(',')
                                  .includes(operation.value),
                          )
                        : [],
                })
                setTime(event.eventType_PersonRescue?.mission?.completedAt)
                setMissionData({
                    missionType:
                        event.eventType_PersonRescue?.mission?.missionType?.replaceAll(
                            '_',
                            ' ',
                        ),
                    description:
                        event.eventType_PersonRescue?.mission?.description,
                    operationOutcome:
                        event.eventType_PersonRescue?.mission?.operationOutcome?.replaceAll(
                            '_',
                            ' ',
                        ),
                    currentLocationID:
                        event.eventType_PersonRescue?.mission?.currentLocation
                            ?.id,
                    operationDescription:
                        event.eventType_PersonRescue?.mission
                            ?.operationDescription,
                })
                setTimeline(
                    event.eventType_PersonRescue?.mission?.missionTimeline
                        ?.nodes,
                )
            }
        },
        onError: (error) => {
            console.error('Error getting current event', error)
        },
    })

    const handleSetMemberList = (members: any) => {
        setMembers(
            members
                ?.filter(
                    (member: any) =>
                        member.archived == false && member.firstName != '',
                )
                ?.map((member: any) => ({
                    label: member.firstName + ' ' + member.surname,
                    value: member.id,
                })),
        )
    }
    if (!offline) {
        getSeaLogsMembersList(handleSetMemberList)
    }

    useEffect(() => {
        if (geoLocations) {
            setLocations(
                geoLocations.map((location: any) => ({
                    label: location.title,
                    value: location.id,
                    latitude: location.lat,
                    longitude: location.long,
                })),
            )
        }
    }, [geoLocations])

    const missions = [
        { label: 'To locate', value: 'To locate' },
        { label: 'To assist', value: 'To assist' },
        { label: 'To save', value: 'To save' },
        { label: 'To rescue', value: 'To rescue' },
        { label: 'To remove', value: 'To remove' },
    ]

    const operationOutcomes = [
        { label: 'Assisted by others', value: 'Assisted by others' },
        { label: 'Assisted on scene', value: 'Assisted on scene' },
        { label: 'Medical treatment', value: 'Medical treatment' },
        { label: 'Safe and well', value: 'Safe and well' },
        { label: 'Not located', value: 'Not located' },
        { label: 'Not recoverable', value: 'Not recoverable' },
        { label: 'Fatality', value: 'Fatality' },
        { label: 'Other', value: 'Other' },
    ]

    const commentTypes = [
        { label: 'General', value: 'General' },
        { label: 'Underway', value: 'Underway' },
        { label: 'On Scene', value: 'On Scene' },
    ]

    const operationType = [
        { label: 'Person in water', value: 'Person in water' },
        { label: 'Lost', value: 'Lost' },
        { label: 'Suicide', value: 'Suicide' },
        { label: 'Medical', value: 'Medical' },
        { label: 'Other', value: 'Other' },
    ]

    const gender = [
        { label: 'Male', value: 'Male' },
        { label: 'Female', value: 'Female' },
        { label: 'Other', value: 'Other' },
    ]

    const handleSaveComments = async () => {
        if (rescueData?.missionID === undefined) {
            toast({
                title: 'Error',
                description:
                    'Please save the event first in order to create timeline!',
                variant: 'destructive',
            })
            setOpenCommentsDialog(false)
            return
        }
        const variables = {
            input: {
                commentType: commentData?.commentType,
                description: content ? content : '',
                time: commentTime
                    ? commentTime
                    : dayjs().format('DD/MM/YYYY HH:mm'),
                authorID: commentData?.authorID,
                missionID: rescueData?.missionID,
            },
        }
        if (commentData?.id > 0) {
            if (offline) {
                await missionTimelineModel.save({
                    id: commentData?.id,
                    ...variables.input,
                })
                setOpenCommentsDialog(false)
                await getCurrentEvent(currentEvent?.id)
            } else {
                updateMissionTimeline({
                    variables: {
                        input: {
                            id: commentData?.id,
                            ...variables.input,
                        },
                    },
                })
            }
        } else {
            if (offline) {
                await missionTimelineModel.save({
                    id: generateUniqueId(),
                    ...variables.input,
                })
                setOpenCommentsDialog(false)
                await getCurrentEvent(currentEvent?.id)
            } else {
                createMissionTimeline({
                    variables: {
                        input: {
                            ...variables.input,
                        },
                    },
                })
            }
        }
    }

    const [createMissionTimeline] = useMutation(CreateMissionTimeline, {
        onCompleted: (response) => {
            setOpenCommentsDialog(false)
            getCurrentEvent(currentEvent?.id)
        },
        onError: (error) => {
            console.error('Error creating mission timeline', error)
        },
    })

    const [updateMissionTimeline] = useMutation(UpdateMissionTimeline, {
        onCompleted: (response) => {
            setOpenCommentsDialog(false)
            getCurrentEvent(currentEvent?.id)
        },
        onError: (error) => {
            console.error('Error updating mission timeline', error)
        },
    })

    const handleEditorChange = (newContent: any) => {
        setContent(newContent)
    }

    const handleSave = async () => {
        const variables = {
            input: {
                personName: rescueData.personName,
                gender: rescueData.gender,
                age: +rescueData.age,
                personDescription: rescueData.personDescription,
                cgMembershipNumber: rescueData.cgMembershipNumber,
                personOtherDetails: rescueData.personOtherDetails,
                cgMembershipType: 'cgnz',
                missionID: +rescueData.missionID,
                operationType: rescueData.operationType
                    ?.map((type: any) => type.value)
                    .join(','),
                operationDescription: rescueData.operationDescription,
            },
        }

        if (currentEvent) {
            if (offline) {
                await tripEventModel.save({
                    id: +currentEvent.id,
                    eventCategory: 'HumanRescue',
                    logBookEntrySectionID: currentTrip.id,
                })

                await getCurrentEvent(currentEvent?.id)
                const personRescueData = await personRescueModel.save({
                    id: +selectedEvent?.eventType_PersonRescueID,
                    ...variables.input,
                })
                const dataToSave = {
                    id:
                        +rescueData.missionID > 0
                            ? +rescueData.missionID
                            : generateUniqueId(),
                    missionType: missionData.missionType,
                    description: missionData.description,
                    operationDescription: missionData.operationDescription,
                    operationOutcome: missionData.operationOutcome,
                    completedAt: time,
                    currentLocationID: missionData.currentLocationID,
                    eventID: +personRescueData?.id,
                    eventType: 'HumanRescue',
                    vesselID: vesselID,
                }
                await cgEventMissionModel.save(dataToSave)
                await getCurrentEvent(currentEvent?.id)
                if (+rescueData.missionID > 0) {
                    updateTripReport({
                        id: tripReport.map((trip: any) => trip.id),
                    })
                } else {
                    updateTripReport({
                        id: [
                            ...tripReport.map((trip: any) => trip.id),
                            currentTrip.id,
                        ],
                    })
                }
            } else {
                updateTripEvent({
                    variables: {
                        input: {
                            id: +currentEvent.id,
                            eventCategory: 'HumanRescue',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
                updateEventType_PersonRescue({
                    variables: {
                        input: {
                            id: +selectedEvent?.eventType_PersonRescueID,
                            ...variables.input,
                        },
                    },
                })
            }
        } else {
            if (offline) {
                const data = await tripEventModel.save({
                    id: generateUniqueId(),
                    eventCategory: 'HumanRescue',
                    logBookEntrySectionID: currentTrip.id,
                })

                setCurrentEvent(data)
                const personRescueData = await personRescueModel.save({
                    id: generateUniqueId(),
                    personName: rescueData.personName,
                    gender: rescueData.gender,
                    age: +rescueData.age,
                    personDescription: rescueData.personDescription,
                    cgMembershipNumber: rescueData.cgMembershipNumber,
                    personOtherDetails: rescueData.personOtherDetails,
                    cgMembershipType: 'cgnz',
                    missionID: +rescueData.missionID,
                    operationType: rescueData.operationType
                        ?.map((type: any) => type.value)
                        .join(','),
                    operationDescription: rescueData.operationDescription,
                })
                await cgEventMissionModel.save({
                    id: generateUniqueId(),
                    missionType: missionData.missionType,
                    description: missionData.description,
                    operationDescription: missionData.operationDescription,
                    operationOutcome: missionData.operationOutcome,
                    completedAt: time,
                    currentLocationID: missionData.currentLocationID,
                    eventID: +personRescueData?.id,
                    eventType: 'HumanRescue',
                    vesselID: vesselID,
                })
                await getCurrentEvent(currentEvent?.id)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
                await tripEventModel.save({
                    id: currentEvent?.id,
                    eventType_PersonRescueID: personRescueData.id,
                })

                await getCurrentEvent(currentEvent?.id)
                closeModal()
            } else {
                createTripEvent({
                    variables: {
                        input: {
                            eventCategory: 'HumanRescue',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }
        }
    }

    const [createTripEvent] = useMutation(CreateTripEvent, {
        onCompleted: (response) => {
            const data = response.createTripEvent
            setCurrentEvent(data)
            createEventType_PersonRescue({
                variables: {
                    input: {
                        personName: rescueData.personName,
                        gender: rescueData.gender,
                        age: +rescueData.age,
                        personDescription: rescueData.personDescription,
                        cgMembershipNumber: rescueData.cgMembershipNumber,
                        personOtherDetails: rescueData.personOtherDetails,
                        cgMembershipType: 'cgnz',
                        missionID: +rescueData.missionID,
                        operationType: rescueData.operationType
                            ?.map((type: any) => type.value)
                            .join(','),
                        operationDescription: rescueData.operationDescription,
                    },
                },
            })
        },
        onError: (error) => {
            console.error('Error creating trip event', error)
        },
    })

    const [createEventType_PersonRescue] = useMutation(
        CreateEventType_PersonRescue,
        {
            onCompleted: (response) => {
                const data = response.createEventType_PersonRescue
                createCGEventMission({
                    variables: {
                        input: {
                            missionType: missionData.missionType,
                            description: missionData.description,
                            operationDescription:
                                missionData.operationDescription,
                            operationOutcome: missionData.operationOutcome,
                            completedAt: time,
                            currentLocationID: missionData.currentLocationID,
                            eventID: +data?.id,
                            eventType: 'HumanRescue',
                            vesselID: vesselID,
                        },
                    },
                })
                updateTripEvent({
                    variables: {
                        input: {
                            id: currentEvent?.id,
                            eventType_PersonRescueID: data.id,
                        },
                    },
                })
                closeModal()
            },
            onError: (error) => {
                console.error('Error creating Person rescue', error)
            },
        },
    )

    const [updateEventType_PersonRescue] = useMutation(
        UpdateEventType_PersonRescue,
        {
            onCompleted: (response) => {
                const data = response.updateEventType_PersonRescue
                if (+rescueData.missionID > 0) {
                    updateCGEventMission({
                        variables: {
                            input: {
                                id: +rescueData.missionID,
                                missionType: missionData.missionType,
                                description: missionData.description,
                                operationDescription:
                                    missionData.operationDescription,
                                operationOutcome: missionData.operationOutcome,
                                completedAt: time,
                                currentLocationID:
                                    missionData.currentLocationID,
                                eventID: +data?.id,
                                eventType: 'HumanRescue',
                                vesselID: vesselID,
                            },
                        },
                    })
                } else {
                    createCGEventMission({
                        variables: {
                            input: {
                                missionType: missionData.missionType,
                                description: missionData.description,
                                operationDescription:
                                    missionData.operationDescription,
                                operationOutcome: missionData.operationOutcome,
                                completedAt: time,
                                currentLocationID:
                                    missionData.currentLocationID,
                                eventID: +data?.id,
                                eventType: 'HumanRescue',
                                vesselID: vesselID,
                            },
                        },
                    })
                }
            },
            onError: (error) => {
                console.error('Error updating person rescue', error)
            },
        },
    )

    const [updateTripEvent] = useMutation(UpdateTripEvent, {
        onCompleted: (response) => {
            getCurrentEvent(currentEvent?.id)
        },
        onError: (error) => {
            console.error('Error updating trip event', error)
        },
    })

    const [createCGEventMission] = useMutation(CreateCGEventMission, {
        onCompleted: (response) => {
            getCurrentEvent(currentEvent?.id)
            updateTripReport({
                id: [...tripReport.map((trip: any) => trip.id), currentTrip.id],
            })
        },
        onError: (error) => {
            console.error('Error creating CG Event Mission', error)
        },
    })

    const [updateCGEventMission] = useMutation(UpdateCGEventMission, {
        onCompleted: (response) => {
            getCurrentEvent(currentEvent?.id)
            updateTripReport({
                id: tripReport.map((trip: any) => trip.id),
            })
        },
        onError: (error) => {
            console.error('Error updating CG Event Mission', error)
        },
    })

    const handleCreateComment = () => {
        if (selectedEvent) {
            setOpenCommentsDialog(true)
            handleEditorChange('')
            setCommentData(false)
        } else {
            toast({
                title: 'Error',
                description:
                    'Please save the event first in order to create timeline!',
                variant: 'destructive',
            })
        }
    }
    const offlineGetSeaLogsMembersList = async () => {
        // getSeaLogsMembersList(handleSetMemberList)
        const members = await seaLogsMemberModel.getAll()
        handleSetMemberList(members)
    }
    useEffect(() => {
        if (offline) {
            offlineGetSeaLogsMembersList()
        }
    }, [offline])
    return (
        <div className="px-0 md:px-4 pt-4">
            <div className="grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                <div className="my-4  col-span-3 md:col-span-1">
                    Target person/s details
                    <p className=" mt-4 max-w-[25rem] leading-loose">
                        Record person name and details
                    </p>
                </div>
                <div className="col-span-3 md:col-span-2">
                    <div className="my-4">
                        {operationType && (
                            <Combobox
                                id="operation-type"
                                options={operationType}
                                multi
                                placeholder="Operation Type"
                                value={rescueData?.operationType}
                                onChange={(value: any) => {
                                    setRescueData({
                                        ...rescueData,
                                        operationType: value,
                                    })
                                }}
                                isDisabled={locked}
                                label="Operation Type"
                            />
                        )}
                    </div>
                    {rescueData?.operationType?.find(
                        (operation: any) => operation.value == 'Other',
                    ) && (
                        <div className="my-4">
                            <Label
                                label="Operation Description"
                                htmlFor="operation-description">
                                <Textarea
                                    id="operation-description"
                                    rows={4}
                                    className="w-full"
                                    placeholder="Operation description"
                                    value={rescueData?.operationDescription}
                                    disabled={locked}
                                    onChange={(e) => {
                                        setRescueData({
                                            ...rescueData,
                                            operationDescription:
                                                e.target.value,
                                        })
                                    }}
                                />
                            </Label>
                        </div>
                    )}
                    <div className="my-4">
                        <div className="flex w-full gap-4">
                            <div className="w-1/2">
                                <Label
                                    label="Person Name"
                                    htmlFor="person-name">
                                    <Input
                                        id="person-name"
                                        type="text"
                                        placeholder="Person Name"
                                        value={
                                            rescueData?.personName
                                                ? rescueData?.personName
                                                : ''
                                        }
                                        disabled={locked}
                                        onChange={(e) => {
                                            setRescueData({
                                                ...rescueData,
                                                personName: e.target.value,
                                            })
                                        }}
                                    />
                                </Label>
                            </div>
                            <div className="w-1/2">
                                <Combobox
                                    id="gender"
                                    options={gender}
                                    placeholder="Gender"
                                    value={
                                        gender?.find(
                                            (location: any) =>
                                                location.value ==
                                                rescueData?.gender,
                                        ) || null
                                    }
                                    onChange={(value: any) => {
                                        setRescueData({
                                            ...rescueData,
                                            gender: value?.value,
                                        })
                                    }}
                                    isDisabled={locked}
                                    label="Gender"
                                />
                            </div>
                        </div>
                    </div>
                    <div className="my-4">
                        <div className="flex w-full gap-4">
                            <div className="w-1/2">
                                <Label label="Age" htmlFor="age">
                                    <Input
                                        id="age"
                                        type="number"
                                        placeholder="Enter age"
                                        min={1}
                                        value={
                                            rescueData?.age
                                                ? rescueData?.age
                                                : ''
                                        }
                                        disabled={locked}
                                        onChange={(e) => {
                                            setRescueData({
                                                ...rescueData,
                                                age: e.target.value,
                                            })
                                        }}
                                    />
                                </Label>
                            </div>
                            <div className="w-1/2">
                                <Label
                                    label="CG Membership Number"
                                    htmlFor="cgMembershipNumber">
                                    <Input
                                        id="cgMembershipNumber"
                                        type="number"
                                        placeholder="Enter CG Membership Number"
                                        min={1}
                                        value={
                                            rescueData?.cgMembershipNumber
                                                ? rescueData?.cgMembershipNumber
                                                : ''
                                        }
                                        disabled={locked}
                                        onChange={(e) => {
                                            setRescueData({
                                                ...rescueData,
                                                cgMembershipNumber:
                                                    e.target.value,
                                            })
                                        }}
                                    />
                                </Label>
                            </div>
                        </div>
                    </div>
                    <div className="my-4">
                        <Label
                            label="Person Description"
                            htmlFor="person-description">
                            <Textarea
                                id="person-description"
                                rows={4}
                                className="w-full"
                                placeholder="Person description"
                                value={rescueData?.personDescription}
                                disabled={locked}
                                onChange={(e) => {
                                    setRescueData({
                                        ...rescueData,
                                        personDescription: e.target.value,
                                    })
                                }}
                            />
                        </Label>
                    </div>
                    <div className="my-4">
                        <Label label="Other Details" htmlFor="other-details">
                            <Textarea
                                id="other-details"
                                rows={4}
                                className="w-full"
                                placeholder="Other details"
                                value={rescueData?.personOtherDetails}
                                disabled={locked}
                                onChange={(e) => {
                                    setRescueData({
                                        ...rescueData,
                                        personOtherDetails: e.target.value,
                                    })
                                }}
                            />
                        </Label>
                    </div>
                </div>
            </div>
            <hr className="my-2" />
            <div className="grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                <div className="my-4  col-span-3 md:col-span-1">
                    Mission
                    <p className=" mt-4 max-w-[25rem] leading-loose">
                        Lorem ipsum dolor sit amet consectetur adipisicing elit.
                        Quod eveniet quaerat voluptates voluptatem quam odio
                        magnam, culpa accusantium at dolore, corrupti rem
                        reiciendis repudiandae cumque veritatis? Blanditiis
                        quibusdam nostrum suscipit?
                    </p>
                </div>
                <div className="col-span-3 md:col-span-2">
                    <div className="my-4">
                        <Combobox
                            id="mission"
                            options={missions}
                            placeholder="Mission Type"
                            value={
                                missions?.find(
                                    (mission: any) =>
                                        mission.value ==
                                        missionData?.missionType,
                                ) || null
                            }
                            onChange={(value: any) => {
                                setMissionData({
                                    ...missionData,
                                    missionType: value?.value,
                                })
                            }}
                            isDisabled={locked}
                            label="Mission Type"
                        />
                    </div>
                    <div className="my-4">
                        <Label
                            label="Mission Description"
                            htmlFor="mission-description">
                            <Textarea
                                id="mission-description"
                                rows={4}
                                className="w-full"
                                placeholder="Mission description"
                                value={missionData?.description}
                                disabled={locked}
                                onChange={(e) => {
                                    setMissionData({
                                        ...missionData,
                                        description: e.target.value,
                                    })
                                }}
                            />
                        </Label>
                    </div>
                    <div className="my-4">
                        <div className="flex flex-col">
                            <Label
                                label="Mission Timeline"
                                htmlFor="mission-timeline"
                                className="mb-1"
                            />
                            <div className="flex gap-4 flex-col">
                                {timeline &&
                                    timeline?.map(
                                        (comment: any, index: number) => (
                                            <div
                                                key={index}
                                                className="flex flex-col gap-4 w-full mb-2">
                                                <div className="flex gap-4 justify-between">
                                                    <div
                                                        className="comment-html"
                                                        dangerouslySetInnerHTML={{
                                                            __html: comment.description,
                                                        }}></div>
                                                    <div className="flex gap-4">
                                                        {comment.author.id >
                                                            0 && (
                                                            <p className="">
                                                                {comment.author
                                                                    .firstName +
                                                                    ' ' +
                                                                    comment
                                                                        .author
                                                                        .surname}
                                                            </p>
                                                        )}
                                                        <p className="">
                                                            {formatDateTime(
                                                                comment.time,
                                                            )}
                                                        </p>
                                                        <Button
                                                            variant="text"
                                                            size="icon"
                                                            iconLeft={
                                                                <Pencil className="w-5 h-5" />
                                                            }
                                                            onClick={() => {
                                                                setOpenCommentsDialog(
                                                                    true,
                                                                )
                                                                setCommentData(
                                                                    comment,
                                                                )
                                                                handleEditorChange(
                                                                    comment.description,
                                                                )
                                                                setCommentTime(
                                                                    formatDateTime(
                                                                        comment.time,
                                                                    ),
                                                                )
                                                            }}
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        ),
                                    )}
                            </div>
                            <div>
                                <Button
                                    variant="text"
                                    iconLeft={<Plus size={20} />}
                                    onClick={
                                        locked ? () => {} : handleCreateComment
                                    }
                                    disabled={locked}>
                                    Add Notes/Comments
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr className="my-2" />
            <div className="grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                <div className="my-4  col-span-3 md:col-span-1">
                    Mission complete
                    <p className=" mt-4 max-w-[25rem] leading-loose">
                        Record the operation outcome, location and time of
                        completion
                    </p>
                </div>
                <div className="col-span-3 md:col-span-2">
                    <div className="my-4">
                        <Combobox
                            id="operation-outcome"
                            options={operationOutcomes}
                            placeholder="Operation outcome"
                            value={
                                operationOutcomes?.find(
                                    (outcome: any) =>
                                        outcome.value ==
                                        missionData?.operationOutcome,
                                ) || null
                            }
                            onChange={(value: any) => {
                                setMissionData({
                                    ...missionData,
                                    operationOutcome: value?.value,
                                })
                            }}
                            isDisabled={locked}
                            label="Operation Outcome"
                        />
                    </div>
                    {missionData?.operationOutcome == 'Other' && (
                        <div className="my-4">
                            <Label
                                label="Operation Outcome Description"
                                htmlFor="operation-outcome-description">
                                <Textarea
                                    id="operation-outcome-description"
                                    rows={4}
                                    className="w-full"
                                    placeholder="Description"
                                    value={missionData?.operationDescription}
                                    disabled={locked}
                                    onChange={(e) => {
                                        setMissionData({
                                            ...missionData,
                                            operationDescription:
                                                e.target.value,
                                        })
                                    }}
                                />
                            </Label>
                        </div>
                    )}
                    <div className="my-4 flex gap-4">
                        <TimePicker
                            label="Time of Completion"
                            value={
                                time &&
                                dayjs(
                                    `${dayjs().format('YYYY-MM-DD')} ${time}`,
                                ).isValid()
                                    ? dayjs(
                                          `${dayjs().format('YYYY-MM-DD')} ${time}`,
                                      ).toDate()
                                    : new Date()
                            }
                            onChange={(date) =>
                                setTime(dayjs(date).format('HH:mm'))
                            }
                            use24Hour={true}
                            disabled={locked}
                            className="w-full"
                            nowButton={!locked}
                            nowButtonLabel="Complete now"
                        />
                    </div>
                    <div className="my-4">
                        {locations && (
                            <Combobox
                                id="completed-geo-location"
                                options={locations}
                                placeholder="Current Location"
                                value={
                                    locations?.find(
                                        (location: any) =>
                                            location.value ==
                                            missionData?.currentLocationID,
                                    ) || null
                                }
                                onChange={(value: any) => {
                                    setMissionData({
                                        ...missionData,
                                        currentLocationID: value?.value,
                                    })
                                }}
                                isDisabled={locked}
                                label="Current Location"
                            />
                        )}
                    </div>
                </div>
            </div>
            <div className="flex justify-end">
                <Button variant="text" onClick={() => closeModal()}>
                    Cancel
                </Button>
                <Button
                    variant="primary"
                    iconLeft={<Check size={20} />}
                    onClick={locked ? () => {} : handleSave}
                    disabled={locked}>
                    {selectedEvent ? 'Update' : 'Save'}
                </Button>
            </div>
            <AlertDialogNew
                openDialog={openCommentsDialog}
                setOpenDialog={setOpenCommentsDialog}
                handleCreate={handleSaveComments}
                actionText={commentData?.id > 0 ? 'Update' : 'Create Comment'}>
                <Heading slot="title" className="text-2xl  leading-6 my-2   ">
                    {commentData?.id > 0
                        ? 'Update Comment'
                        : 'Create New Comment'}
                </Heading>
                <div className="my-4">
                    <Combobox
                        id="comment-type"
                        options={commentTypes}
                        placeholder="Comment type"
                        value={
                            commentTypes?.find(
                                (type: any) =>
                                    type.value ==
                                    commentData?.commentType?.replaceAll(
                                        '_',
                                        ' ',
                                    ),
                            ) || null
                        }
                        onChange={(value: any) =>
                            setCommentData({
                                ...commentData,
                                commentType: value?.value,
                            })
                        }
                        label="Comment Type"
                    />
                </div>
                <div className="mb-4">
                    <DatePicker
                        onChange={(date) => {
                            if (date) {
                                setCommentTime(
                                    dayjs(date).format('DD/MM/YYYY HH:mm'),
                                )
                            }
                        }}
                        mode="single"
                        type="datetime"
                        label="Comment Time"
                        value={
                            commentTime
                                ? dayjs(
                                      commentTime,
                                      'DD/MM/YYYY HH:mm',
                                  ).toDate()
                                : new Date()
                        }
                        placeholder="Select date and time"
                        dateFormat="dd/MM/yyyy HH:mm"
                        closeOnSelect={true}
                    />
                </div>

                <Editor
                    id="comment"
                    placeholder="Comment"
                    className="w-full"
                    content={content}
                    handleEditorChange={handleEditorChange}
                />

                <div className="flex items-center">
                    {members && (
                        <Combobox
                            id="comment-author"
                            options={members}
                            placeholder="Crew member"
                            value={
                                members?.find(
                                    (member: any) =>
                                        member.value == commentData?.author?.id,
                                ) || null
                            }
                            onChange={(value: any) =>
                                setCommentData({
                                    ...commentData,
                                    authorID: value?.value,
                                })
                            }
                            label="Crew member"
                        />
                    )}
                </div>
            </AlertDialogNew>
        </div>
    )
}
