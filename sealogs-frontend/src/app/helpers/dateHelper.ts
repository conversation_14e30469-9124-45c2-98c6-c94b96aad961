import dayjs from 'dayjs'
import { isEmpty, trim } from 'lodash'

/**
 * Formats a given date string or Date object into a string.
 * If the date string is in 'YYYY-MM-DD' format, it converts it
 * to 'DD/MM/YY' or 'DD/MM/YYYY' format without timezone conversion.
 * For other formats, it uses dayjs to parse and format.
 *
 * @param dateString - The date input, which can be a string, Date object, or any type.
 *                     Defaults to an empty string.
 * @param twoDigitYear - Boolean indicating whether to use a two-digit year format.
 *                       Defaults to true.
 * @returns A string representing the formatted date in 'DD/MM/YY' or 'DD/MM/YYYY' format,
 *          or an empty string for invalid inputs.
 */

export const formatDate = (dateString: any = '', twoDigitYear = true) => {
    if (isEmpty(trim(dateString))) {
        return ''
    }

    // For date-only strings (YYYY-MM-DD), use pure string manipulation to avoid any timezone conversion
    if (
        typeof dateString === 'string' &&
        /^\d{4}-\d{2}-\d{2}$/.test(dateString)
    ) {
        const [yearStr, monthStr, dayStr] = dateString.split('-')
        const year = twoDigitYear ? yearStr.slice(-2) : yearStr

        // Return in DD/MM/YY or DD/MM/YYYY format (Australian/NZ preference from memories)
        // Ensure day and month have leading zeros (2 digits)
        const day = parseInt(dayStr, 10).toString().padStart(2, '0')
        const month = parseInt(monthStr, 10).toString().padStart(2, '0')

        return `${day}/${month}/${year}`
    }

    // For other date formats, use dayjs to parse and format
    let dayjsDate: any

    if (dateString && typeof dateString === 'object') {
        // Handle object input
        dayjsDate = dayjs(dateString.toString())
    } else {
        // Handle other string formats or date objects
        dayjsDate = dayjs(dateString)
    }

    if (!dayjsDate.isValid()) {
        return ''
    }

    const day = dayjsDate.format('DD') // DD gives day with leading zero
    const month = dayjsDate.format('MM') // MM gives month with leading zero
    const year = twoDigitYear
        ? dayjsDate.format('YY')
        : dayjsDate.format('YYYY')

    // Return in DD/MM/YY or DD/MM/YYYY format
    return `${day}/${month}/${year}`
}

/**
 * Formats a given date string or Date object into a string in the format of
 * DD/MM/YY HH:mm or DD/MM/YYYY HH:mm, depending on the twoDigitYear parameter.
 * If the date string is in 'YYYY-MM-DD' format, it converts it to
 * 'DD/MM/YY HH:mm' or 'DD/MM/YYYY HH:mm' format without timezone conversion,
 * and adds a default time of 00:00. For other formats, it uses dayjs to parse
 * and format.
 *
 * @param dateString - The date input, which can be a string, Date object, or any type.
 *                     Defaults to an empty string.
 * @param twoDigitYear - Boolean indicating whether to use a two-digit year format.
 *                       Defaults to true.
 * @returns A string representing the formatted date in 'DD/MM/YY HH:mm' or
 *          'DD/MM/YYYY HH:mm' format, or an empty string for invalid inputs.
 */
export const formatDateTime = (dateString: any = '', twoDigitYear = true) => {
    if (isEmpty(trim(dateString))) {
        return ''
    }

    // For date-only strings (YYYY-MM-DD), use pure string manipulation and add default time
    if (
        typeof dateString === 'string' &&
        /^\d{4}-\d{2}-\d{2}$/.test(dateString)
    ) {
        const [yearStr, monthStr, dayStr] = dateString.split('-')
        const year = twoDigitYear ? yearStr.slice(-2) : yearStr

        // Return in DD/MM/YY HH:mm or DD/MM/YYYY HH:mm format
        // Ensure day and month have leading zeros (2 digits)
        const day = parseInt(dayStr, 10).toString().padStart(2, '0')
        const month = parseInt(monthStr, 10).toString().padStart(2, '0')

        // Default time to 00:00 for date-only strings
        return `${day}/${month}/${year} 00:00`
    }

    // For other date formats, use dayjs to parse and format
    let dayjsDate: any

    if (dateString && typeof dateString === 'object') {
        // Handle object input
        dayjsDate = dayjs(dateString.toString())
    } else {
        // Handle other string formats or date objects
        dayjsDate = dayjs(dateString)
    }

    if (!dayjsDate.isValid()) {
        return ''
    }

    const day = dayjsDate.format('DD') // DD gives day with leading zero
    const month = dayjsDate.format('MM') // MM gives month with leading zero
    const year = twoDigitYear
        ? dayjsDate.format('YY')
        : dayjsDate.format('YYYY')
    const time = dayjsDate.format('HH:mm') // 24-hour format with leading zeros

    // Return in DD/MM/YY HH:mm or DD/MM/YYYY HH:mm format
    return `${day}/${month}/${year} ${time}`
}

export const formatDBDateTime = (dateString: any = '') => {
    if (isEmpty(trim(dateString))) {
        return ''
    }
    return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * Parse date strings without timezone conversion
 * Handles null, undefined, empty strings, and date-only strings (YYYY-MM-DD)
 * For date-only strings, uses Date constructor with individual components to avoid timezone interpretation
 * @param dateString - Date string, Date object, null, undefined, or empty string
 * @returns Date object or null for invalid/empty inputs
 */
export const parseDateWithoutTimezone = (
    dateString: string | Date | null | undefined,
): Date | null => {
    // Handle null, undefined, or empty strings
    if (
        !dateString ||
        (typeof dateString === 'string' && isEmpty(trim(dateString)))
    ) {
        return null
    }

    // If already a Date object, return as-is
    if (dateString instanceof Date) {
        return dateString
    }

    // For date-only strings (YYYY-MM-DD), create Date using local calendar date
    if (
        typeof dateString === 'string' &&
        /^\d{4}-\d{2}-\d{2}$/.test(dateString)
    ) {
        // Parse the date components manually to avoid any timezone interpretation
        const [year, month, day] = dateString.split('-').map(Number)

        // Create Date using local timezone constructor (month is 0-indexed)
        // This creates a Date object representing the exact calendar date in local time
        const date = new Date(year, month - 1, day)
        return isNaN(date.getTime()) ? null : date
    }

    // For other formats, fall back to regular Date constructor
    const date = new Date(dateString)
    return isNaN(date.getTime()) ? null : date
}
